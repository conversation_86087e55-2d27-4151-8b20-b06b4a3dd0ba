// FullscreenToggleButton.razor.js - JavaScript интеграция для компонента FullscreenToggleButton
import { FullscreenPlugin } from "./Plugins/FullscreenPlugin.js";

/**
 * Менеджер FullscreenPlugin плагинов для FullscreenToggleButton компонентов
 * Обеспечивает изоляцию между множественными экземплярами плееров
 */
class FullscreenPluginManager {
  constructor() {
    this.pluginInstances = new Map(); // Map<cameraId, FullscreenPlugin>
    this.dotNetRefs = new Map(); // Map<cameraId, DotNetObjectReference>
  }

  /**
   * Инициализирует FullscreenPlugin плагин для камеры
   * @param {string} cameraId - ID камеры
   * @param {DotNetObjectReference} dotNetRef - ссылка на C# компонент
   * @param {Object} options - настройки плагина
   */
  async initializePlugin(cameraId, dotNetRef, options = {}) {
    try {
      console.log(`[FullscreenPlugin] Инициализация для камеры ${cameraId}`, options);

      // Получаем MediaPipeline для данной камеры
      const mediaPipeline = this.getMediaPipelineForCamera(cameraId);
      if (!mediaPipeline) {
        console.warn(`[FullscreenPlugin] MediaPipeline не найден для камеры ${cameraId}`);
        return;
      }

      // Создаем экземпляр FullscreenPlugin плагина
      const fullscreenPlugin = new FullscreenPlugin(mediaPipeline, {
        autoTrackChanges: true,
        emitEvents: true,
        ...options
      });

      // Подписываемся на события изменения полноэкранного режима
      fullscreenPlugin.on('fullscreen-changed', async (event) => {
        await this.handleFullscreenChanged(cameraId, event);
      });

      // Добавляем плагин к MediaPipeline
      mediaPipeline.addPlugin('fullscreenPlugin', fullscreenPlugin);

      // Сохраняем экземпляры
      this.pluginInstances.set(cameraId, fullscreenPlugin);
      this.dotNetRefs.set(cameraId, dotNetRef);

      // Уведомляем C# о начальном состоянии
      const initialState = fullscreenPlugin.getFullscreenState();
      await this.notifyFullscreenChanged(cameraId, initialState);

      console.log(`[FullscreenPlugin] Плагин инициализирован для камеры ${cameraId}`);
    } catch (error) {
      console.error(`[FullscreenPlugin] Ошибка инициализации для камеры ${cameraId}:`, error);
    }
  }

  /**
   * Останавливает FullscreenPlugin плагин для камеры
   * @param {string} cameraId - ID камеры
   */
  stopPlugin(cameraId) {
    try {
      const fullscreenPlugin = this.pluginInstances.get(cameraId);
      if (fullscreenPlugin) {
        // Удаляем плагин из MediaPipeline
        const mediaPipeline = this.getMediaPipelineForCamera(cameraId);
        if (mediaPipeline) {
          mediaPipeline.removePlugin('fullscreenPlugin');
        }
        
        this.pluginInstances.delete(cameraId);
      }

      this.dotNetRefs.delete(cameraId);

      console.log(`[FullscreenPlugin] Плагин остановлен для камеры ${cameraId}`);
    } catch (error) {
      console.error(`[FullscreenPlugin] Ошибка при остановке плагина для камеры ${cameraId}:`, error);
    }
  }

  /**
   * Переключает полноэкранный режим для указанной камеры
   * @param {string} cameraId - ID камеры
   * @returns {Promise<boolean>} Новое состояние полноэкранного режима
   */
  async toggleFullscreen(cameraId) {
    try {
      const fullscreenPlugin = this.pluginInstances.get(cameraId);
      if (!fullscreenPlugin) {
        console.warn(`[FullscreenPlugin] Плагин не найден для камеры ${cameraId}`);
        return false;
      }

      const newState = await fullscreenPlugin.toggleFullscreen();
      console.log(`[FullscreenPlugin] Полноэкранный режим переключен для камеры ${cameraId}: ${newState}`);
      
      return newState;
    } catch (error) {
      console.error(`[FullscreenPlugin] Ошибка при переключении полноэкранного режима для камеры ${cameraId}:`, error);
      
      // Уведомляем C# об ошибке
      await this.notifyError(cameraId, `Ошибка полноэкранного режима: ${error.message}`);
      
      return false;
    }
  }

  /**
   * Получает текущее состояние полноэкранного режима для камеры
   * @param {string} cameraId - ID камеры
   * @returns {boolean} Состояние полноэкранного режима
   */
  getFullscreenState(cameraId) {
    const fullscreenPlugin = this.pluginInstances.get(cameraId);
    return fullscreenPlugin ? fullscreenPlugin.getFullscreenState() : false;
  }

  /**
   * Получает MediaPipeline для указанной камеры
   * @param {string} cameraId - ID камеры
   * @returns {MediaPipeline|null} - экземпляр MediaPipeline или null
   */
  getMediaPipelineForCamera(cameraId) {
    // Ищем плеер по ID камеры в глобальном реестре (из MsePlayer.razor.js)
    if (window.players && typeof window.players.get === 'function') {
      const player = window.players.get(cameraId);
      if (player && player.pipe) {
        console.log(`[FullscreenPlugin] MediaPipeline найден через глобальный реестр для камеры ${cameraId}`);
        return player.pipe;
      }
    }

    // Альтернативный поиск через DOM элементы
    const videoElements = document.querySelectorAll('video');
    for (const video of videoElements) {
      // Проверяем, есть ли у видео элемента связанный MediaPipeline
      if (video._mediaPipeline || video.mediaPipeline) {
        const pipeline = video._mediaPipeline || video.mediaPipeline;
        console.log(`[FullscreenPlugin] MediaPipeline найден через DOM для камеры ${cameraId}`);
        return pipeline;
      }
    }

    // Попробуем найти через глобальные переменные
    if (window.currentPlayer && window.currentPlayer.pipe) {
      console.log(`[FullscreenPlugin] MediaPipeline найден через currentPlayer для камеры ${cameraId}`);
      return window.currentPlayer.pipe;
    }

    console.warn(`[FullscreenPlugin] MediaPipeline не найден для камеры ${cameraId}. Доступные объекты:`, {
      windowPlayers: !!window.players,
      playersType: typeof window.players,
      currentPlayer: !!window.currentPlayer,
      videoElements: document.querySelectorAll('video').length
    });
    
    return null;
  }

  /**
   * Обрабатывает изменение полноэкранного режима от FullscreenPlugin
   * @param {string} cameraId - ID камеры
   * @param {Object} eventData - данные события
   */
  async handleFullscreenChanged(cameraId, eventData) {
    await this.notifyFullscreenChanged(cameraId, eventData.isFullscreen);
  }

  /**
   * Уведомляет C# компонент об изменении полноэкранного режима
   * @param {string} cameraId - ID камеры
   * @param {boolean} isFullscreen - новое состояние полноэкранного режима
   */
  async notifyFullscreenChanged(cameraId, isFullscreen) {
    try {
      const dotNetRef = this.dotNetRefs.get(cameraId);
      if (!dotNetRef) {
        console.warn(`[FullscreenPlugin] DotNetRef не найден для камеры ${cameraId}`);
        return;
      }

      // Вызываем метод C# компонента
      await dotNetRef.invokeMethodAsync('OnFullscreenStateChanged', isFullscreen);
    } catch (error) {
      // Не логируем ошибки JSDisconnectedException, так как они нормальны при навигации
      if (!error.message?.includes('JSDisconnectedException')) {
        console.error(`[FullscreenPlugin] Ошибка при уведомлении C# об изменении полноэкранного режима для камеры ${cameraId}:`, error);
      }
    }
  }

  /**
   * Уведомляет C# компонент об ошибке
   * @param {string} cameraId - ID камеры
   * @param {string} errorMessage - сообщение об ошибке
   */
  async notifyError(cameraId, errorMessage) {
    try {
      const dotNetRef = this.dotNetRefs.get(cameraId);
      if (!dotNetRef) {
        console.warn(`[FullscreenPlugin] DotNetRef не найден для камеры ${cameraId}`);
        return;
      }

      // Вызываем метод C# компонента для обработки ошибки
      await dotNetRef.invokeMethodAsync('OnError', errorMessage);
    } catch (error) {
      if (!error.message?.includes('JSDisconnectedException')) {
        console.error(`[FullscreenPlugin] Ошибка при уведомлении C# об ошибке для камеры ${cameraId}:`, error);
      }
    }
  }

  /**
   * Получает информацию о плагине для камеры
   * @param {string} cameraId - ID камеры
   * @returns {Object|null} - информация о плагине или null
   */
  getPluginInfo(cameraId) {
    const fullscreenPlugin = this.pluginInstances.get(cameraId);
    return fullscreenPlugin ? fullscreenPlugin.getInfo() : null;
  }

  /**
   * Освобождает все ресурсы
   */
  dispose() {
    for (const [cameraId, fullscreenPlugin] of this.pluginInstances) {
      try {
        const mediaPipeline = this.getMediaPipelineForCamera(cameraId);
        if (mediaPipeline) {
          mediaPipeline.removePlugin('fullscreenPlugin');
        }
      } catch (error) {
        console.error(`[FullscreenPlugin] Ошибка при освобождении ресурсов для камеры ${cameraId}:`, error);
      }
    }
    
    this.pluginInstances.clear();
    this.dotNetRefs.clear();
    
    console.log('[FullscreenPlugin] Все ресурсы освобождены');
  }
}

// Создаем глобальный экземпляр менеджера
const fullscreenPluginManager = new FullscreenPluginManager();

// Экспортируем функции для использования из C#
export function initializeFullscreenPlugin(cameraId, dotNetRef, options) {
  return fullscreenPluginManager.initializePlugin(cameraId, dotNetRef, options);
}

export function stopFullscreenPlugin(cameraId) {
  return fullscreenPluginManager.stopPlugin(cameraId);
}

export function toggleFullscreen(cameraId) {
  return fullscreenPluginManager.toggleFullscreen(cameraId);
}

export function getFullscreenState(cameraId) {
  return fullscreenPluginManager.getFullscreenState(cameraId);
}

export function getFullscreenPluginInfo(cameraId) {
  return fullscreenPluginManager.getPluginInfo(cameraId);
}

// Автоматическая очистка при выгрузке страницы
window.addEventListener('beforeunload', () => {
  fullscreenPluginManager.dispose();
});

console.log('[FullscreenPlugin] JavaScript модуль загружен');
