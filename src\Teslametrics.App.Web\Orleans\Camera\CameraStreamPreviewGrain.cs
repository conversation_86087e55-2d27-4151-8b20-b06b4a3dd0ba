using FFMpegNET;
using Orleans.Concurrency;
using Orleans.Streams;

namespace Teslametrics.App.Web.Orleans.Camera;

public class CameraStreamPreviewGrain : Grain, ICameraStreamPreviewGrain
{
    private readonly ILogger<CameraStreamPreviewGrain> _logger;
    private readonly MicroSegmentPreviewGenerator _previewGenerator;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private byte[]? _preview;
    private IAsyncStream<MicroSegment>? _videoStream;

    public CameraStreamPreviewGrain(ILogger<CameraStreamPreviewGrain> logger,
                                    MicroSegmentPreviewGenerator previewGenerator,
                                    IServiceScopeFactory serviceScopeFactory)
    {
        _logger = logger;
        _previewGenerator = previewGenerator;
        _serviceScopeFactory = serviceScopeFactory;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        var streamProvider = this.GetStreamProvider(StreamNames.VideoLiveStream);

        var provider = this.GetStreamProvider(StreamNames.VideoLiveStream);
        _videoStream = provider.GetStream<MicroSegment>(StreamId.Create(StreamNamespaces.CameraStreams, this.GetPrimaryKey()));

        var sub = await _videoStream.SubscribeAsync((segment, token) =>
        {
            var microSegment = new FFMpegNET.MicroSegment(segment.Payload, segment.StartTime, segment.Duration);
            _preview = _previewGenerator.GeneratePreview(microSegment);

            return Task.CompletedTask;
        });

        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        _previewGenerator.Dispose();
        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    public Task<byte[]?> GetPreviewAsync()
    {
        return Task.FromResult(_preview);
    }
}

[Alias("Teslametrics.App.Web.Orleans.ICameraStreamPreviewGrain")]
public interface ICameraStreamPreviewGrain : IGrainWithGuidKey
{
    [ReadOnly]
    [Alias("GetPreviewAsync")]
    public Task<byte[]?> GetPreviewAsync();
}