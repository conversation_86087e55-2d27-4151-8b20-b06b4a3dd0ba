namespace Teslametrics.App.Web.Orleans.Camera;

[<PERSON><PERSON>("Teslametrics.App.Web.Orleans.IMediaServerGrain")]
public interface IMediaServerGrain : IGrainWithGuidKey
{
    [<PERSON>as("ConnectRtspAsync")]
    public Task ConnectRtspAsync(CameraConnectRtspRequest request);

    [<PERSON><PERSON>("ConnectOnvifAsync")]
    public Task ConnectOnvifAsync(CameraConnectOnvifRequest request);

    [<PERSON><PERSON>("DisconnectAsync")]
    public Task DisconnectAsync(CameraDisconnectRequest request);

    [<PERSON><PERSON>("StopAllAsync")]
    public Task StopAllAsync();

    [<PERSON><PERSON>("GetStatusAsync")]
    public Task<CameraStatus> GetStatusAsync(GetCameraStatusRequest request);

    [GenerateSerializer]
    [<PERSON><PERSON>("Teslametrics.App.Web.Orleans.IMediaServerGrain.CameraConnectRtspRequest")]
    public record CameraConnectRtspRequest(Guid CameraId, string ArchiveUri, string ViewUri, string PublicUri);

    [GenerateSerializer]
    [<PERSON><PERSON>("Teslametrics.App.Web.Orleans.IMediaServerGrain.CameraConnectOnvifRequest")]
    public record CameraConnectOnvifRequest(Guid CameraId, string Host, int Port, string Username, string Password);

    [GenerateSerializer]
    [Alias("Teslametrics.App.Web.Orleans.IMediaServerGrain.CameraDisconnectRequest")]
    public record CameraDisconnectRequest(Guid CameraId);

    [GenerateSerializer]
    [Alias("Teslametrics.App.Web.Orleans.IMediaServerGrain.GetCameraStatusRequest")]
    public record GetCameraStatusRequest(Guid CameraId);
}