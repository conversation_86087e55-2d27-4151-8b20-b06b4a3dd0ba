stages:
  - prerequest
  - migration
  - deploy

.prerequest_job: &prerequest_job
  when: manual
  interruptible: true
  timeout: 1h
  stage: prerequest
  script:
    - echo "Running prerequests"

# Шаблон для миграции базы данных
.migration_job: &migration_job
  when: always
  interruptible: true
  timeout: 1h
  stage: migration
  script:
    - echo "Running database migrations"
    - export ASPNETCORE_ENVIRONMENT=Migration
    - cd src/Teslametrics.DbMigrator
    - dotnet run

# Шаблон для развертывания проекта
.deploy_job: &deploy_job
  when: on_success
  interruptible: true
  timeout: 1h
  stage: deploy

# Предустановки
perftest-prerequest:
  <<: *prerequest_job
  script:
    - echo "Running prerequests"
    - cd eng
    - docker compose -f env-snmd-compose.yml down
    - docker compose -f env-snmd-compose.yml up -d
  tags:
    - perftest
  only:
    refs:
      - perftest

# Миграция базы данных
dev-db-migration:
  <<: *migration_job
  tags:
    - dev
  only:
    refs:
      - dev

caviardev-db-migration:
  <<: *migration_job
  tags:
    - stage
  only:
    refs:
      - wirenboard
  when: manual

perftest-db-migration:
  <<: *migration_job
  tags:
    - perftest
  only:
    refs:
      - perftest
  when: manual

# Развертывание проекта
dev-deploy-project:
  <<: *deploy_job
  script:
    - echo "Deploying project"
    - cd eng
    - docker compose -f app-development-compose.yml up -d --build
    - docker system prune -a -f
  tags:
    - dev
  only:
    refs:
      - dev

caviardev-deploy-project:
  <<: *deploy_job
  script:
    - echo "Deploying project"
    - cd eng
    - docker compose -f app-caviardev-compose.yml up -d --build
    - docker system prune -a -f
  tags:
    - stage
  only:
    refs:
      - wirenboard
  when: manual

perftest-deploy-project:
  <<: *deploy_job
  script:
    - echo "Deploying project"
    - cd eng
    - docker compose -f app-perftest-compose.yml down
    #- docker compose -f dotnet-monitor/dotnet-monitor.yml up -d
    #- sleep 15
    - docker compose -f app-perftest-compose.yml build --no-cache
    - docker compose -f app-perftest-compose.yml up -d
    - docker builder prune -f
    - docker image prune -f
    #- docker system prune -a -f
    #- docker run --device-read-bps /dev/vda:30mb --device-write-bps /dev/vda:30mb --name teslametrics-app-web --network teslametrics_default -p 80:80 -p 443:443 -p 22022:22 -e ASPNETCORE_ENVIRONMENT=Perftest -e DOTNET_EnableDiagnostics=1 --user root --restart always teslametrics-app
  tags:
    - perftest
  only:
    refs:
      - perftest
  when: manual
