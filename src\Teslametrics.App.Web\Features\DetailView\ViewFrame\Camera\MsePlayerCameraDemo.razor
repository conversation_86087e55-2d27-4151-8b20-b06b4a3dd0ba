@page "/mse-player-camera-demo"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components
@using Microsoft.JSInterop
@using MudBlazor

@attribute [Authorize]
@inherits InteractiveBaseComponent

<MudContainer MaxWidth="MaxWidth.Large"
              Class="mt-4">
    <MudText Typo="Typo.h4"
             Class="mb-4">Демонстрация MSE плеера с реальной камерой</MudText>

    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h6"
                 Class="mb-2">Параметры камеры</MudText>

        <MudGrid>
            <MudItem xs="12"
                     sm="6">
                <MudTextField @bind-Value="CameraId"
                              Label="ID камеры"
                              Variant="Variant.Outlined"
                              HelperText="Введите GUID камеры"
                              Class="mb-2" />
            </MudItem>

            <MudItem xs="12"
                     sm="6">
                <MudSelect @bind-Value="StreamType"
                           Label="Тип потока"
                           Variant="Variant.Outlined"
                           Class="mb-2">
                    <MudSelectItem Value="1">Archive</MudSelectItem>
                    <MudSelectItem Value="2">View</MudSelectItem>
                    <MudSelectItem Value="4">Public</MudSelectItem>
                </MudSelect>
            </MudItem>
        </MudGrid>
    </MudPaper>

    <MudPaper Class="pa-4">
        <MudText Typo="Typo.h6"
                 Class="mb-2">MSE плеер</MudText>
        @if (string.IsNullOrEmpty(AppliedCameraId))
        {
            <MudText Typo="Typo.body2"
                     Class="mb-4">
                Введите ID камеры и нажмите "Применить настройки" для просмотра потока с камеры.
            </MudText>
        }
        else
        {
            <MudText Typo="Typo.body2"
                     Class="mb-4">
                Просмотр потока с камеры ID: @AppliedCameraId, тип потока: @Orleans.Camera.StreamType.View.GetName()
            </MudText>

            <!-- MSE плеер -->
            <div class="mb-4">
                <MsePlayer @ref="msePlayer"
                           CameraId="@Guid.Parse(AppliedCameraId)"
                           Type="@Orleans.Camera.StreamType.View">
                    <ToolbarContent>
                        <PlayToggleButton Player="context" />
                        <VolumeComponent Player="context"
                                         ShowForced="true" />
                        <SeekToLiveButton Player="context" />
                        <MudSpacer />
                        <FullscreenToggleButton Player="context" />
                    </ToolbarContent>
                </MsePlayer>
            </div>

            <!-- Временная шкала -->
            @if (msePlayer != null)
            {
                <VideoTimeline Player="msePlayer"
                               UpdateInterval="250"
                               WindowSizeHours="3" />
            }
        }
    </MudPaper>
</MudContainer>

@code {
    private string CameraId { get; set; } = "0196d41c-828c-3db3-2bb9-4ec14d9f05fe";
    private int StreamType { get; set; } = 1; // Archive по умолчанию

    private string AppliedCameraId { get; set; } = "0196d41c-828c-3db3-2bb9-4ec14d9f05fe";
    private int AppliedStreamType { get; set; } = 1;

    private MsePlayer? msePlayer;
}