using System.Reactive;
using Microsoft.AspNetCore.Components;
using Teslametrics.App.Web.Extensions;

namespace Teslametrics.App.Web.Features.Main.Cameras.List.CameraCard.Preview;

public partial class CameraPreview
{
    [Parameter]
    [EditorRequired]
    public Guid CameraId { get; set; }

    [Parameter]
    [EditorRequired]
    public Guid? CameraStreamId { get; set; }

    private SubscribeCameraPreviewUseCase.Response? _subscriptionResult;

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        await SubscribeAsync();
    }

    private async Task SubscribeAsync()
    {
        Unsubscribe();
        try
        {
            if (!CameraStreamId.HasValue || CameraStreamId == Guid.Empty) return;

            _subscriptionResult = await ScopeFactory.MediatorSend(new SubscribeCameraPreviewUseCase.Request(Observer.Create<object>(OnNext, OnError), CameraStreamId.Value));
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
        }

        switch (_subscriptionResult?.Result)
        {
            case SubscribeCameraPreviewUseCase.Result.Success:
                CompositeDisposable.Add(_subscriptionResult.Subscription!);
                break;
            case SubscribeCameraPreviewUseCase.Result.ValidationError:
                Snackbar.Add("Ошибка валидации при подписке на события", MudBlazor.Severity.Error);
                break;
            case SubscribeCameraPreviewUseCase.Result.Unknown:
                Logger.LogError("Unexpected error in {Component}, {UseCase}", nameof(CameraPreview), nameof(SubscribeCameraPreviewUseCase));
                Snackbar.Add($"Не удалось получить подписку на события камеры из-за непредвиденной ошибки ответа от сервера. Обратитесь к администратору.", MudBlazor.Severity.Error);
                break;
            default:
                Logger.LogError("Unexpected error in {Component}, {UseCase}. Result: {Result}", nameof(CameraPreview), nameof(SubscribeCameraPreviewUseCase), _subscriptionResult?.Result);
                Snackbar.Add($"Не удалось получить подписку на события камеры из-за ошибки: {_subscriptionResult?.Result}", MudBlazor.Severity.Error);
                break;
        }
    }

    private void Unsubscribe()
    {
        if (_subscriptionResult?.Subscription is not null)
        {
            CompositeDisposable.Remove(_subscriptionResult.Subscription);
            _subscriptionResult?.Subscription?.Dispose();
        }
    }

    public void OnNext(object value)
    {
        StateHasChanged();
    }

    public void OnError(Exception error)
    {
        Snackbar.Add("Ошибка при подписке на события", MudBlazor.Severity.Error);
        Logger.LogError(error, error.Message);
    }
}
