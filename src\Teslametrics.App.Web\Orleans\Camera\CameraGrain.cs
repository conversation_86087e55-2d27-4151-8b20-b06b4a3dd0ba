using Teslametrics.App.Web.Exceptions;
using Teslametrics.App.Web.Orleans.Camera.Events;
using Teslametrics.App.Web.Services.Outbox;
using static Teslametrics.App.Web.Orleans.Camera.ICameraGrain;

namespace Teslametrics.App.Web.Orleans.Camera;

public class CameraGrain : Grain, ICameraGrain
{
    private readonly ILogger<CameraGrain> _logger;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private RtspStateMachine _rtspStateMachine;
    private readonly OnvifStateMachine _onvifStateMachine;
    private readonly CancellationTokenSource _cts;
    private CameraStatus _cameraStatus = CameraStatus.Stopped;
    private Task? _updateStatusTask;
    private static readonly TimeSpan _updateInterval = TimeSpan.FromSeconds(1);

    public CameraGrain(ILoggerFactory loggerFactory,
                       IServiceScopeFactory serviceScopeFactory)
    {
        _logger = loggerFactory.CreateLogger<CameraGrain>();

        _serviceScopeFactory = serviceScopeFactory;
        _rtspStateMachine = new RtspStateMachine(loggerFactory.CreateLogger<RtspStateMachine>(), this.GetPrimaryKey(), serviceScopeFactory);
        _onvifStateMachine = new OnvifStateMachine(loggerFactory.CreateLogger<OnvifStateMachine>(), this.GetPrimaryKey(), serviceScopeFactory);
        _cts = new CancellationTokenSource();
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        _updateStatusTask = Task.Run(() => UpdateStatusAsync(_cts.Token));
        await _rtspStateMachine.OnActivateAsync(cancellationToken);
        await _onvifStateMachine.OnActivateAsync(cancellationToken);
        await base.OnActivateAsync(cancellationToken);
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        _cts.Cancel();
        await _updateStatusTask!;
        await _rtspStateMachine.OnDeactivateAsync(cancellationToken);
        await _onvifStateMachine.OnDeactivateAsync(cancellationToken);
        await base.OnDeactivateAsync(reason, cancellationToken);
    }

    public Task<CameraStatus> GetStatusAsync() =>
        Task.FromResult(_cameraStatus);

    public Task ConnectRtspAsync(ConnectRtspRequest request)
    {
        return _rtspStateMachine.ConnectAsync(request.ArchiveUri, request.ViewUri, request.PublicUri);
    }

    public Task ConnectOnvifAsync(ConnectOnvifRequest request)
    {
        return _onvifStateMachine.ConnectAsync(request.Host, request.Port, request.Username, request.Password);
    }

    public async Task DisconnectAsync()
    {
        await _rtspStateMachine.DisconnectAsync();
        await _onvifStateMachine.DisconnectAsync();
    }

    public Task<GetCameraStreamIdResponse> GetCameraStreamIdAsync(GetCameraStreamIdRequest request) =>
         Task.FromResult(new GetCameraStreamIdResponse(_rtspStateMachine.GetCameraStreamGrain(request.StreamType)?.GetPrimaryKey()));

    private async Task UpdateStatusAsync(CancellationToken cancellationToken)
    {
        var currentTime = DateTime.UtcNow;

        while (!cancellationToken.IsCancellationRequested)
        {
            var state = (await _rtspStateMachine.GetStatusAsync(), _onvifStateMachine.GetStatus());

            var status = state switch
            {
                (RtspStateMachine.Status.Problem, _) => CameraStatus.Problem,
                (_, OnvifStateMachine.Status.Problem) => CameraStatus.Problem,
                (RtspStateMachine.Status.Stopped, OnvifStateMachine.Status.Disabled) => CameraStatus.Stopped,
                (RtspStateMachine.Status.Starting, OnvifStateMachine.Status.Disabled) => CameraStatus.Starting,
                (RtspStateMachine.Status.Running, OnvifStateMachine.Status.Disabled) => CameraStatus.Running,
                (RtspStateMachine.Status.Stopped, OnvifStateMachine.Status.Stopped) => CameraStatus.Stopped,
                (RtspStateMachine.Status.Starting, _) => CameraStatus.Starting,
                (_, OnvifStateMachine.Status.Starting) => CameraStatus.Starting,
                (RtspStateMachine.Status.Running, OnvifStateMachine.Status.Running) => CameraStatus.Running,
                _ => CameraStatus.Problem
            };

            if (_cameraStatus != status)
            {
                _cameraStatus = status;

                // TODO: Replace Outbox
                List<object> @event = [_cameraStatus switch
                {
                    CameraStatus.Stopped => new CameraStoppedEvent(this.GetPrimaryKey()),
                    CameraStatus.Running => new CameraRunningEvent(this.GetPrimaryKey()),
                    CameraStatus.Starting => new CameraStartingEvent(this.GetPrimaryKey()),
                    CameraStatus.Problem => new CameraProblemEvent(this.GetPrimaryKey()),
                    _ => throw new AppException("Invalid camera state")
                }];

                using var scope = _serviceScopeFactory.CreateScope();
                var outbox = scope.ServiceProvider.GetRequiredService<IOutbox>();
                await outbox.AddRangeAsync(@event);
            }

            var deltaTime = DateTime.UtcNow - currentTime;

            var remainingTime = _updateInterval - deltaTime;

            if (remainingTime > TimeSpan.Zero)
            {
                await Task.Delay(remainingTime);
            }

            currentTime = DateTime.UtcNow;
        }
    }
}

public enum CameraStatus
{
    Stopped,
    Starting,
    Running,
    Problem
}